import React, { useState, useRef, useEffect } from 'react';
import './InteractiveTranscript.css';

const InteractiveTranscript = ({
  subtitles,
  currentSegmentIndex,
  onSegmentClick,
  currentTime,
  onSegmentUpdate
}) => {
  const [editMode, setEditMode] = useState(false);
  const [focusMode, setFocusMode] = useState(false);
  const [editingSegment, setEditingSegment] = useState(null);
  const [editedText, setEditedText] = useState('');
  const [editedStart, setEditedStart] = useState(0);
  const [editedEnd, setEditedEnd] = useState(0);
  
  const transcriptRef = useRef(null);
  const activeSegmentRef = useRef(null);

  // Scroll to active segment when it changes
  useEffect(() => {
    if (activeSegmentRef.current && transcriptRef.current && (focusMode || editingSegment === currentSegmentIndex)) {
      const container = transcriptRef.current;
      const activeElement = activeSegmentRef.current;
      
      const containerRect = container.getBoundingClientRect();
      const activeRect = activeElement.getBoundingClientRect();
      
      // Check if active element is not fully visible
      if (
        activeRect.top < containerRect.top ||
        activeRect.bottom > containerRect.bottom
      ) {
        // Calculate scroll position to center the active element
        const scrollTop = activeElement.offsetTop - container.clientHeight / 2 + activeElement.clientHeight / 2;
        container.scrollTop = scrollTop;
      }
    }
  }, [currentSegmentIndex, focusMode, editingSegment]);

  const handleSegmentClick = (index) => {
    if (!editMode) {
      onSegmentClick(index);
    }
  };

  const startEditing = (index) => {
    const segment = subtitles[index];
    setEditingSegment(index);
    setEditedText(segment.text);
    setEditedStart(segment.start);
    setEditedEnd(segment.end);
  };

  const saveEdits = () => {
    if (editingSegment !== null) {
      const updatedSegment = {
        ...subtitles[editingSegment],
        text: editedText,
        start: parseFloat(editedStart),
        end: parseFloat(editedEnd)
      };
      
      onSegmentUpdate(editingSegment, updatedSegment);
      setEditingSegment(null);
    }
  };

  const cancelEdits = () => {
    setEditingSegment(null);
  };

  const formatTime = (seconds) => {
    if (!seconds && seconds !== 0) return '00:00.000';
    
    const mins = Math.floor(seconds / 60);
    const secs