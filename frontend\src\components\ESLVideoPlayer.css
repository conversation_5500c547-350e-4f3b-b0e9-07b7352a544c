/* Custom styles for ESL Video Player */

/* Mode dropdown styling */
.mode-dropdown {
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
  color: white;
  border: none;
  border-radius: 20px;
  padding: 6px 24px 6px 12px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  cursor: pointer;
  outline: none;
  appearance: none;
  background-image: none;
  position: relative;
}

.mode-dropdown:hover {
  background: linear-gradient(135deg, #7c3aed, #6d28d9);
}

.mode-dropdown:focus {
  box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.3);
}

/* Speed dropdown styling */
.speed-dropdown {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 6px;
  padding: 4px 20px 4px 8px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  outline: none;
  appearance: none;
  background-image: none;
  backdrop-filter: blur(4px);
}

.speed-dropdown:hover {
  background: rgba(255, 255, 255, 0.3);
}

.speed-dropdown:focus {
  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.3);
}

/* Control button styling */
.control-button {
  background: rgba(255, 255, 255, 0.15);
  border: none;
  border-radius: 50%;
  padding: 10px;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.control-button:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: scale(1.1);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.control-button:disabled {
  opacity: 0.4;
  cursor: not-allowed;
  transform: none;
}

.control-button:disabled:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: none;
  box-shadow: none;
}

/* Special styling for play button */
.play-button {
  background: rgba(255, 255, 255, 0.2);
  padding: 12px;
}

.play-button:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.15);
}

/* Mode button styling for ESL controls */
.mode-button {
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  color: #475569;
  border: 2px solid #e2e8f0;
  border-radius: 16px;
  padding: 14px 28px;
  font-size: 14px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.mode-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.5s ease;
}

.mode-button:hover::before {
  left: 100%;
}

.mode-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border-color: #cbd5e1;
}

.mode-button.active {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  border-color: #2563eb;
  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.4);
}

.mode-button.active:hover {
  background: linear-gradient(135deg, #2563eb, #1e40af);
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(59, 130, 246, 0.5);
}

/* Custom Progress Bar Styling */
.custom-progress-bar {
  width: 100%;
  padding: 8px 0;
  z-index: 30;
  position: relative;
}

.progress-track {
  width: 100%;
  height: 6px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.progress-track:hover {
  height: 8px;
  background: rgba(255, 255, 255, 0.3);
}

.buffered-progress {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: rgba(255, 255, 255, 0.4);
  border-radius: 3px;
  transition: width 0.3s ease;
}

.play-progress {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
  border-radius: 3px;
  transition: width 0.1s ease;
}

.progress-handle {
  position: absolute;
  right: -6px;
  top: 50%;
  transform: translateY(-50%);
  width: 12px;
  height: 12px;
  background: #ffffff;
  border: 2px solid #3b82f6;
  border-radius: 50%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.progress-track:hover .progress-handle {
  opacity: 1;
}

/* Subtitle overlay styling - Control panel style */
.subtitle-overlay {
  background: rgba(0, 0, 0, 0.75);
  backdrop-filter: blur(16px);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 12px;
  padding: 20px 32px;
  text-align: center;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
  max-width: 80%;
  margin: 0 auto;
}

/* Control bar gradient - Match subtitle section header theme */
.control-bar {
  background: linear-gradient(
    to top,
    rgba(59, 130, 246, 0.95) 0%,
    rgba(79, 70, 229, 0.85) 40%,
    rgba(99, 102, 241, 0.6) 70%,
    rgba(129, 140, 248, 0.3) 90%,
    transparent 100%
  );
  backdrop-filter: blur(12px);
  border-top: 1px solid rgba(59, 130, 246, 0.3);
}

/* Dropdown arrow styling */
.dropdown-arrow {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
  width: 12px;
  height: 12px;
  color: currentColor;
}

/* Custom scrollbar for subtitle section */
.subtitle-section::-webkit-scrollbar {
  width: 6px;
}

.subtitle-section::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.subtitle-section::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.subtitle-section::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Control Panel Styles - Separate from overlay controls */
.custom-progress-bar-control {
  width: 100%;
  padding: 4px 0;
  position: relative;
}

.progress-track-control {
  width: 100%;
  height: 6px;
  background: rgba(148, 163, 184, 0.3);
  border-radius: 6px;
  position: relative;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.progress-track-control:hover {
  height: 8px;
  background: rgba(148, 163, 184, 0.4);
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.15);
}

.buffered-progress-control {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: rgba(148, 163, 184, 0.5);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.play-progress-control {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
  border-radius: 8px;
  transition: width 0.1s ease;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.progress-handle-control {
  position: absolute;
  right: -8px;
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  background: #ffffff;
  border: 2px solid #3b82f6;
  border-radius: 50%;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.4);
  opacity: 1;
  transition: all 0.2s ease;
  cursor: grab;
}

.progress-track-control:hover .progress-handle-control {
  transform: translateY(-50%) scale(1.1);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

/* Dragging states */
.progress-track-control.dragging {
  cursor: grabbing;
}

.progress-track-control.dragging .progress-handle-control {
  opacity: 1;
  transform: translateY(-50%) scale(1.2);
  box-shadow: 0 4px 16px rgba(59, 130, 246, 0.4);
}

.progress-handle-control {
  cursor: grab;
}

.progress-handle-control.dragging {
  cursor: grabbing;
  transform: translateY(-50%) scale(1.2);
  box-shadow: 0 4px 16px rgba(59, 130, 246, 0.4);
}

/* Always show handle when dragging */
.progress-track-control.dragging .progress-handle-control,
.progress-handle-control.dragging {
  opacity: 1 !important;
}

/* Control Panel Button Styling - Harmonized */
.control-button-panel {
  background: rgba(148, 163, 184, 0.15);
  border: none;
  border-radius: 12px;
  padding: 12px;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid rgba(148, 163, 184, 0.2);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.control-button-panel:hover {
  background: rgba(148, 163, 184, 0.25);
  transform: scale(1.05);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
}

.control-button-panel:disabled {
  opacity: 0.4;
  cursor: not-allowed;
  transform: none;
}

.control-button-panel:disabled:hover {
  background: rgba(148, 163, 184, 0.2);
  transform: none;
  box-shadow: none;
}

/* Special styling for play button in control panel */
.play-button-panel {
  background: rgba(59, 130, 246, 0.2);
  padding: 14px;
  border: 1px solid rgba(59, 130, 246, 0.3);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
}

.play-button-panel:hover {
  background: rgba(59, 130, 246, 0.3);
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.3);
}

/* Speed dropdown for control panel */
.speed-dropdown-panel {
  background: rgba(148, 163, 184, 0.15);
  color: white;
  border: 1px solid rgba(148, 163, 184, 0.2);
  border-radius: 10px;
  padding: 8px 28px 8px 12px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  outline: none;
  appearance: none;
  background-image: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.speed-dropdown-panel:hover {
  background: rgba(148, 163, 184, 0.25);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.speed-dropdown-panel:focus {
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3);
}

/* Compact Control Styles - For integrated navbar */
.control-button-compact {
  background: rgba(148, 163, 184, 0.15);
  border: none;
  border-radius: 8px;
  padding: 8px;
  color: white;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid rgba(148, 163, 184, 0.2);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.control-button-compact:hover {
  background: rgba(148, 163, 184, 0.25);
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.control-button-compact:disabled {
  opacity: 0.4;
  cursor: not-allowed;
  transform: none;
}

.control-button-compact:disabled:hover {
  background: rgba(148, 163, 184, 0.15);
  transform: none;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

/* Special styling for compact play button */
.play-button-compact {
  background: rgba(59, 130, 246, 0.2);
  padding: 10px;
  border: 1px solid rgba(59, 130, 246, 0.3);
  box-shadow: 0 3px 10px rgba(59, 130, 246, 0.2);
}

.play-button-compact:hover {
  background: rgba(59, 130, 246, 0.3);
  transform: scale(1.1);
  box-shadow: 0 5px 16px rgba(59, 130, 246, 0.3);
}

/* Mode buttons for compact layout */
.mode-button-compact {
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.2s ease;
  border: none;
  cursor: pointer;
}

/* Speed dropdown for compact layout */
.speed-dropdown-compact {
  background: rgba(148, 163, 184, 0.15);
  color: white;
  border: 1px solid rgba(148, 163, 184, 0.2);
  border-radius: 6px;
  padding: 6px 20px 6px 8px;
  font-size: 11px;
  font-weight: 500;
  cursor: pointer;
  outline: none;
  appearance: none;
  background-image: none;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.speed-dropdown-compact:hover {
  background: rgba(148, 163, 184, 0.25);
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.15);
}

.speed-dropdown-compact:focus {
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3);
}

/* Modern Control Panel Styles - Completely rebuilt */
.modern-progress-track {
  width: 100%;
  height: 6px;
  background: rgba(148, 163, 184, 0.3);
  border-radius: 6px;
  position: relative;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.modern-progress-track:hover {
  height: 8px;
  background: rgba(148, 163, 184, 0.4);
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.15);
}

.modern-buffered-progress {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: rgba(148, 163, 184, 0.5);
  border-radius: 6px;
  transition: width 0.3s ease;
}

.modern-play-progress {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
  border-radius: 6px;
  transition: width 0.1s ease;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.modern-progress-handle {
  position: absolute;
  right: -8px;
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  background: #ffffff;
  border: 2px solid #3b82f6;
  border-radius: 50%;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.4);
  opacity: 1;
  transition: all 0.2s ease;
  cursor: grab;
}

.modern-progress-track:hover .modern-progress-handle {
  transform: translateY(-50%) scale(1.1);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.modern-progress-track.dragging {
  cursor: grabbing;
}

.modern-progress-track.dragging .modern-progress-handle {
  opacity: 1;
  transform: translateY(-50%) scale(1.2);
  box-shadow: 0 4px 16px rgba(59, 130, 246, 0.4);
  cursor: grabbing;
}

.modern-progress-handle.dragging {
  cursor: grabbing;
  transform: translateY(-50%) scale(1.2);
  box-shadow: 0 4px 16px rgba(59, 130, 246, 0.4);
}

/* Modern Control Buttons */
.modern-control-btn {
  background: rgba(148, 163, 184, 0.15);
  border: none;
  border-radius: 8px;
  padding: 8px;
  color: white;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid rgba(148, 163, 184, 0.2);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  height: 40px;
  width: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modern-control-btn:hover {
  background: rgba(148, 163, 184, 0.25);
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.modern-control-btn:disabled {
  opacity: 0.4;
  cursor: not-allowed;
  transform: none;
}

.modern-control-btn:disabled:hover {
  background: rgba(148, 163, 184, 0.15);
  transform: none;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

/* Volume Slider Styles */
.modern-volume-slider {
  width: 80px;
  height: 4px;
  background: rgba(148, 163, 184, 0.3);
  border-radius: 2px;
  outline: none;
  cursor: pointer;
  transition: all 0.3s ease;
  -webkit-appearance: none;
  appearance: none;
}

.modern-volume-slider::-webkit-slider-track {
  width: 100%;
  height: 4px;
  background: rgba(148, 163, 184, 0.3);
  border-radius: 2px;
  border: none;
}

.modern-volume-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 14px;
  height: 14px;
  background: #ffffff;
  border: 2px solid #3b82f6;
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 2px 6px rgba(59, 130, 246, 0.3);
  transition: all 0.2s ease;
}

.modern-volume-slider::-webkit-slider-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 3px 10px rgba(59, 130, 246, 0.4);
}

.modern-volume-slider::-moz-range-track {
  width: 100%;
  height: 4px;
  background: rgba(148, 163, 184, 0.3);
  border-radius: 2px;
  border: none;
}

.modern-volume-slider::-moz-range-thumb {
  width: 14px;
  height: 14px;
  background: #ffffff;
  border: 2px solid #3b82f6;
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 2px 6px rgba(59, 130, 246, 0.3);
  transition: all 0.2s ease;
}

.modern-volume-slider::-moz-range-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 3px 10px rgba(59, 130, 246, 0.4);
}

.modern-volume-slider:hover {
  background: rgba(148, 163, 184, 0.4);
}

/* Modern Play Button */
.modern-play-btn {
  background: rgba(59, 130, 246, 0.2);
  border: 1px solid rgba(59, 130, 246, 0.3);
  box-shadow: 0 3px 10px rgba(59, 130, 246, 0.2);
  height: 44px;
  width: 44px;
}

.modern-play-btn:hover {
  background: rgba(59, 130, 246, 0.3);
  transform: scale(1.1);
  box-shadow: 0 5px 16px rgba(59, 130, 246, 0.3);
}

/* Modern Mode Buttons */
.modern-mode-btn {
  padding: 8px 16px;
  border-radius: 8px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.2s ease;
  border: none;
  cursor: pointer;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Modern Time Display */
.modern-time-display {
  background: rgba(148, 163, 184, 0.15);
  color: white;
  padding: 8px 12px;
  border-radius: 8px;
  font-size: 13px;
  font-weight: 500;
  border: 1px solid rgba(148, 163, 184, 0.2);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Modern Speed Dropdown */
.modern-speed-dropdown {
  background: rgba(148, 163, 184, 0.15);
  color: white;
  border: 1px solid rgba(148, 163, 184, 0.2);
  border-radius: 8px;
  padding: 8px 12px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  outline: none;
  appearance: none;
  background-image: none;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  height: 36px;
  min-width: 60px;
}

.modern-speed-dropdown:hover {
  background: rgba(148, 163, 184, 0.25);
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.15);
}

.modern-speed-dropdown:focus {
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .mode-dropdown {
    padding: 4px 16px 4px 8px;
    font-size: 10px;
  }

  .speed-dropdown {
    padding: 3px 16px 3px 6px;
    font-size: 11px;
  }

  .speed-dropdown-panel {
    padding: 4px 20px 4px 8px;
    font-size: 11px;
  }

  .speed-dropdown-compact {
    padding: 4px 16px 4px 6px;
    font-size: 10px;
  }

  .control-button {
    padding: 8px;
  }

  .control-button-panel {
    padding: 8px;
  }

  .control-button-compact {
    padding: 6px;
  }

  .play-button {
    padding: 10px;
  }

  .play-button-panel {
    padding: 10px;
  }

  .play-button-compact {
    padding: 8px;
  }

  .mode-button-compact {
    padding: 4px 8px;
    font-size: 10px;
  }

  .modern-control-btn {
    padding: 6px;
    height: 32px;
    width: 32px;
  }

  .modern-play-btn {
    height: 36px;
    width: 36px;
  }

  .modern-mode-btn {
    padding: 6px 12px;
    font-size: 10px;
    height: 32px;
  }

  .modern-time-display {
    padding: 6px 10px;
    font-size: 12px;
    height: 32px;
  }

  .modern-speed-dropdown {
    padding: 6px 10px;
    font-size: 11px;
    height: 32px;
    min-width: 50px;
  }

  .subtitle-overlay {
    padding: 16px 20px;
    margin: 0 16px;
    max-width: 90%;
  }

  .mode-button {
    padding: 10px 16px;
    font-size: 12px;
  }

  .progress-track-control {
    height: 4px;
  }

  .progress-track-control:hover {
    height: 6px;
  }

  .progress-handle-control {
    width: 12px;
    height: 12px;
    right: -6px;
  }

  .modern-progress-track {
    height: 4px;
  }

  .modern-progress-track:hover {
    height: 6px;
  }

  .modern-progress-handle {
    width: 12px;
    height: 12px;
    right: -6px;
  }
}
